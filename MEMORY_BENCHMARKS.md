# Memory Size Impact Benchmarks

This document explains the new memory benchmarking scripts that measure how memory usage affects Morph Cloud's snapshot and instance creation performance.

## Overview

The memory benchmarks test Morph Cloud's Infinibranch technology under different memory loads to demonstrate:

- How snapshot creation time scales with memory usage
- How instance startup time is affected by memory content
- The efficiency of Morph's state preservation across varying memory loads
- Performance characteristics for AI agents with different memory requirements

## Scripts

### 1. `quick_memory_benchmark.py` (Recommended for Testing)

**Purpose**: Quick validation of memory impact with smaller, manageable memory loads.

**Memory Scenarios**:
- **Baseline**: No additional memory load (minimal Python process)
- **10MB Load**: Small numpy array (10MB of random float64 data)
- **100MB Load**: Medium numpy array (100MB of random float64 data)

**Usage**:
```bash
python3 quick_memory_benchmark.py
```

**Runtime**: ~5-10 minutes (depending on Morph Cloud response times)

**VM Memory Allocation**: Automatically scales (256MB minimum + memory load + 128MB overhead)

### 2. `memory_size_benchmark.py` (Comprehensive Testing)

**Purpose**: Full-scale testing with realistic memory loads including very large datasets.

**Memory Scenarios**:
- **Baseline**: No additional memory load
- **1MB Load**: Small numpy array (1MB of random float64 data)
- **1GB Load**: Large numpy array (1GB of random float64 data)

**Usage**:
```bash
python3 memory_size_benchmark.py
```

**Runtime**: ~15-30 minutes (1GB memory loading and testing takes time)

**VM Memory Allocation**: Automatically scales (up to 1.2GB+ for the 1GB test)

## How It Works

### Memory Loading Process

1. **Instance Creation**: Creates a Morph Cloud instance with appropriate memory allocation
2. **Numpy Installation**: Installs numpy in the VM instance
3. **Memory Allocation**: Creates numpy arrays filled with random data:
   ```python
   # Example for 100MB load
   elements = (100 * 1024 * 1024) // 8  # 8 bytes per float64
   data = np.random.random(elements)
   ```
4. **Memory Persistence**: Keeps the data in memory with a background process that periodically accesses it
5. **Performance Testing**: Measures snapshot and startup times with the memory load active

### Test Methodology

For each memory scenario:

1. **Setup Phase**:
   - Create VM instance with sufficient memory
   - Install numpy
   - Load specified amount of data into memory
   - Verify memory loading was successful

2. **Snapshot Testing** (5 tests per scenario):
   - Create snapshots of the running instance with memory load
   - Measure time for each snapshot creation
   - Clean up test snapshots

3. **Startup Testing** (5 tests per scenario):
   - Create instances from memory-loaded snapshots
   - Measure startup time for each new instance
   - Verify state preservation (memory content intact)
   - Clean up test instances

4. **Analysis**:
   - Calculate averages, medians, min/max times
   - Compare performance across memory scenarios
   - Show relative impact vs baseline

## Expected Results

Based on Morph Cloud's Infinibranch technology, you should see:

### Snapshot Creation
- **Baseline**: ~200-300ms (typical Morph performance)
- **Small loads (1-100MB)**: Minimal impact, ~250-400ms
- **Large loads (1GB)**: Some impact but still sub-second, ~500-1000ms

### Instance Startup
- **Baseline**: ~500-1000ms (much faster than traditional VMs)
- **Small loads**: Minimal impact, ~600-1200ms
- **Large loads**: Some impact but still very fast, ~1000-2000ms

### Key Insights
- Memory size has **logarithmic impact** rather than linear
- Morph's technology remains **orders of magnitude faster** than traditional VMs even with large memory loads
- **Perfect state preservation** - memory content is exactly restored
- **Scalability** - technology works efficiently across memory sizes

## Interpreting Results

### Performance Ratios
The scripts calculate performance ratios vs baseline:
- **1.0x**: No performance impact
- **1.5x**: 50% slower than baseline
- **2.0x**: 2x slower than baseline

### What Good Results Look Like
- Snapshot ratios: 1.0x - 3.0x (even 1GB should be <3x baseline)
- Startup ratios: 1.0x - 2.0x (memory loading is more efficient on startup)
- Absolute times: Still sub-second for most operations

### Troubleshooting

**Memory Errors**: If you see memory allocation errors:
- The VM doesn't have enough memory allocated
- Try the quick benchmark first (smaller loads)
- Check your Morph Cloud account limits

**Slow Performance**: If operations take much longer than expected:
- Check your internet connection
- Verify Morph Cloud service status
- Try running during off-peak hours

**Installation Errors**: If numpy installation fails:
- The VM image might not have pip/python properly configured
- Try running the basic benchmarks first to verify setup

## Use Cases

### AI Agent Development
- **Memory-intensive agents**: Test how your agent's memory usage affects branching performance
- **Model loading**: Understand impact of loading large ML models into memory
- **Data processing**: Measure performance with large datasets in memory

### Performance Planning
- **Capacity planning**: Understand memory vs performance tradeoffs
- **Architecture decisions**: Choose optimal memory configurations
- **Cost optimization**: Balance memory usage with performance requirements

## Technical Details

### Memory Calculation
```python
# For target memory size in MB
elements_needed = (size_mb * 1024 * 1024) // 8  # 8 bytes per float64
data = np.random.random(elements_needed)
actual_size = data.nbytes / (1024 * 1024)  # Verify actual size
```

### VM Memory Allocation
```python
vm_memory = max(256, memory_load_mb + 128)  # Base + load + overhead
```

### State Verification
The scripts verify that memory content is preserved across snapshots by:
- Calculating checksums of data portions
- Comparing checksums before and after snapshot/restore
- Ensuring background processes continue running

This demonstrates Morph Cloud's **perfect state preservation** - not just that the process restarts, but that the exact memory content is maintained.
