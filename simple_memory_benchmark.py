#!/usr/bin/env python3
"""
Simple Memory Size Impact Test

A version that uses basic Python (no numpy) to demonstrate memory impact:
- Baseline (no load)
- 10MB load (using basic Python lists)
- 100MB load (using basic Python lists)

This version works without external dependencies.
"""

import time
import statistics
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Try to import the real MorphCloudClient
try:
    from morphcloud.api import MorphCloudClient
    print("Using real MorphCloudClient")
except ImportError:
    print("❌ Error: morphcloud package not available")
    exit(1)

def time_operation(name, func, *args, **kwargs):
    """Time an operation and return duration in milliseconds"""
    print(f"⏱️  {name}...", end=" ", flush=True)
    start = time.time()
    result = func(*args, **kwargs)
    duration = (time.time() - start) * 1000
    print(f"{duration:.1f}ms")
    return duration, result

def create_memory_script(size_mb):
    """Create a script to load memory using basic Python"""
    if size_mb == 0:
        return """
import time
print("Baseline: No memory load")
while True: 
    time.sleep(1)
"""
    
    # Calculate how many 1KB strings we need
    kb_strings_needed = size_mb * 1024
    
    return f"""
import time
print("Loading {size_mb}MB using Python lists...")

try:
    # Create {size_mb}MB of data using lists of strings
    data = []
    kb_string = 'x' * 1024  # 1KB string
    
    for i in range({kb_strings_needed}):
        data.append(kb_string)
        if i % 1000 == 0:
            print(f"Loaded {{i//1024}}MB...")
    
    actual_size_mb = len(data) * len(data[0]) / (1024 * 1024)
    print(f"Successfully loaded {{actual_size_mb:.1f}}MB into memory")
    print(f"Data structure: {{len(data)}} strings of {{len(data[0])}} bytes each")
    
    # Keep the data in memory and the process alive
    counter = 0
    while True:
        # Occasionally access the data to ensure it stays in memory
        if counter % 10 == 0:
            checksum = len(data[0]) if data else 0
            print(f"Memory check {{counter}}: data_size={{len(data)}}, checksum={{checksum}}")
        counter += 1
        time.sleep(1)
        
except MemoryError as e:
    print(f"MemoryError: {{e}}")
    exit(1)
except Exception as e:
    print(f"Error: {{e}}")
    exit(1)
"""

def test_memory_scenario(client, memory_mb, scenario_name):
    """Test one memory scenario"""
    print(f"\n{'='*50}")
    print(f"🧪 Testing {scenario_name} ({memory_mb}MB)")
    print(f"{'='*50}")
    
    results = {'snapshot_times': [], 'startup_times': []}
    
    try:
        # Create instance with appropriate memory
        vm_memory = max(256, memory_mb + 128)
        
        print(f"📸 Creating base snapshot (VM: {vm_memory}MB)...")
        duration, snapshot = time_operation(
            "Base snapshot",
            client.snapshots.create,
            image_id="morphvm-minimal",
            vcpus=1,
            memory=vm_memory,
            disk_size=700
        )
        
        duration, instance = time_operation(
            "Instance startup",
            client.instances.start,
            snapshot.id
        )
        
        # Install Python (already available in most images)
        print("📦 Setting up Python environment...")
        instance.exec("apt update && apt install -y python3")
        
        print(f"💾 Loading {memory_mb}MB...")
        script = create_memory_script(memory_mb)
        instance.exec(f"cat > /tmp/load.py << 'EOF'\n{script}\nEOF")
        instance.exec("nohup python3 /tmp/load.py > /tmp/load.log 2>&1 &")
        
        if memory_mb > 0:
            print(f"⏳ Waiting 10s for memory to load...")
            time.sleep(10)
            log = instance.exec("tail -3 /tmp/load.log").stdout
            print(f"📋 Status: {log.strip()}")
        
        # Test snapshots (3 tests)
        print(f"\n📸 Testing snapshot creation...")
        for i in range(3):
            duration, test_snap = time_operation(
                f"Snapshot {i+1}",
                instance.snapshot
            )
            if duration:
                results['snapshot_times'].append(duration)
            if test_snap:
                time.sleep(1)  # Brief wait
                try:
                    test_snap.delete()
                except Exception as e:
                    print(f"    ⚠️  Warning: Could not delete test snapshot: {str(e)[:100]}...")
        
        # Create snapshot for startup tests
        duration, final_snap = time_operation(
            "Final snapshot",
            instance.snapshot
        )
        
        # Test startups (3 tests)
        print(f"\n🚀 Testing instance startup...")
        test_instances = []
        for i in range(3):
            duration, test_inst = time_operation(
                f"Startup {i+1}",
                client.instances.start,
                final_snap.id
            )
            if duration:
                results['startup_times'].append(duration)
            if test_inst:
                test_instances.append(test_inst)
        
        # Cleanup
        print("🧹 Cleaning up...")
        try:
            instance.stop()
        except Exception as e:
            print(f"    ⚠️  Warning: Could not stop main instance: {str(e)[:50]}...")
        
        for inst in test_instances:
            try:
                inst.stop()
            except Exception as e:
                print(f"    ⚠️  Warning: Could not stop test instance: {str(e)[:50]}...")
        
        time.sleep(2)  # Wait before deleting snapshots
        try:
            snapshot.delete()
        except Exception as e:
            print(f"    ⚠️  Warning: Could not delete base snapshot: {str(e)[:50]}...")
        
        try:
            final_snap.delete()
        except Exception as e:
            print(f"    ⚠️  Warning: Could not delete final snapshot: {str(e)[:50]}...")
        
        return results
        
    except Exception as e:
        print(f"❌ Error in {scenario_name}: {e}")
        return results

def main():
    if not os.getenv('MORPH_API_KEY'):
        print("❌ Error: MORPH_API_KEY must be set in .env file")
        return
    
    print("🧠 Simple Memory Size Impact Test")
    print("=================================")
    print("Testing how memory load affects Morph Cloud performance")
    print("Using basic Python (no external dependencies)\n")
    
    client = MorphCloudClient()
    
    # Test scenarios
    scenarios = [
        (0, "Baseline"),
        (10, "10MB Load"),
        (50, "50MB Load")  # Reduced from 100MB for faster testing
    ]
    
    all_results = {}
    
    for memory_mb, name in scenarios:
        all_results[name] = test_memory_scenario(client, memory_mb, name)
    
    # Print results
    print("\n" + "="*60)
    print("🏆 SIMPLE MEMORY IMPACT RESULTS")
    print("="*60)
    
    for scenario, results in all_results.items():
        if results['snapshot_times'] and results['startup_times']:
            snap_avg = statistics.mean(results['snapshot_times'])
            start_avg = statistics.mean(results['startup_times'])
            
            print(f"\n📊 {scenario}:")
            print(f"  📸 Snapshot avg: {snap_avg:.1f}ms")
            print(f"  🚀 Startup avg:  {start_avg:.1f}ms")
    
    # Compare to baseline
    if ('Baseline' in all_results and 
        all_results['Baseline']['snapshot_times'] and 
        all_results['Baseline']['startup_times']):
        baseline_snap = statistics.mean(all_results['Baseline']['snapshot_times'])
        baseline_start = statistics.mean(all_results['Baseline']['startup_times'])
        
        print(f"\n📈 Impact vs Baseline:")
        for scenario, results in all_results.items():
            if (scenario != 'Baseline' and 
                results['snapshot_times'] and 
                results['startup_times']):
                snap_avg = statistics.mean(results['snapshot_times'])
                start_avg = statistics.mean(results['startup_times'])
                
                snap_ratio = snap_avg / baseline_snap
                start_ratio = start_avg / baseline_start
                
                print(f"  {scenario}:")
                print(f"    📸 Snapshot: {snap_ratio:.2f}x baseline")
                print(f"    🚀 Startup:  {start_ratio:.2f}x baseline")
    else:
        print(f"\n⚠️  Could not compare to baseline - insufficient data")
    
    print(f"\n💡 This demonstrates Morph Cloud's efficiency across memory loads!")
    print(f"   Even with significant memory usage, operations remain fast!")

if __name__ == "__main__":
    main()
