#!/bin/bash

echo "🚀 Setting up Morph Cloud Example"
echo "================================="

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    exit 1
fi

# Install required packages
echo "📦 Installing required packages..."
pip3 install morphcloud python-dotenv numpy

echo ""
echo "✅ Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Sign up for a free account at https://cloud.morph.so"
echo "2. Get your API key from the dashboard"
echo "3. Set your API key (choose one option):"
echo "   • Add to .env file: MORPH_API_KEY='your-api-key-here'"
echo "   • Or export: export MORPH_API_KEY='your-api-key-here'"
echo "4. Run examples and benchmarks:"
echo "   python3 morph_example.py              # Basic state preservation demo"
echo "   python3 quick_benchmark.py            # Quick performance test"
echo "   python3 quick_memory_benchmark.py     # Memory impact test"
echo ""
echo "🎯 What the examples demonstrate:"
echo "• Perfect state preservation of running processes"
echo "• Sub-second instance creation from snapshots"
echo "• Memory size impact on performance"
echo "• Infinibranch technology for AI agent development"
