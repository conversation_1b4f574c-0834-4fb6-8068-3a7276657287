#!/bin/bash

echo "🚀 Setting up Morph Cloud Example"
echo "================================="

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is required but not installed."
    exit 1
fi

# Install required packages
echo "📦 Installing required packages..."
pip3 install morphcloud python-dotenv

echo ""
echo "✅ Setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Sign up for a free account at https://cloud.morph.so"
echo "2. Get your API key from the dashboard"
echo "3. Set your API key (choose one option):"
echo "   • Add to .env file: MORPH_API_KEY='your-api-key-here'"
echo "   • Or export: export MORPH_API_KEY='your-api-key-here'"
echo "4. Run the example:"
echo "   python3 morph_example.py"
echo ""
echo "🎯 What the example demonstrates:"
echo "• Perfect state preservation of running processes"
echo "• Sub-second instance creation from snapshots"
echo "• Infinibranch technology for AI agent development"
