#!/usr/bin/env python3
"""
Morph Cloud Memory Size Impact Benchmark

This script measures how memory usage affects snapshot and instance creation times by:
- Creating instances with different memory configurations
- Loading numpy arrays of 1MB and 1GB sizes into memory
- Measuring snapshot creation times with different memory loads
- Comparing instance startup times from memory-loaded snapshots

This demonstrates how Morph's Infinibranch technology handles varying memory loads.
"""

import time
import statistics
import numpy as np
from morphcloud.api import MorphCloudClient
from dotenv import load_dotenv
import os

# Load environment variables from .env file
load_dotenv()

class MemorySizeBenchmark:
    def __init__(self):
        if not os.getenv('MORPH_API_KEY'):
            raise ValueError("MORPH_API_KEY must be set in .env file or environment")
        
        self.client = MorphCloudClient()
        self.results = {
            'baseline': {
                'snapshot_times': [],
                'startup_times': [],
                'memory_usage': 0
            },
            '1mb_load': {
                'snapshot_times': [],
                'startup_times': [],
                'memory_usage': 1  # MB
            },
            '1gb_load': {
                'snapshot_times': [],
                'startup_times': [],
                'memory_usage': 1024  # MB
            }
        }
    
    def time_operation(self, operation_name, operation_func, *args, **kwargs):
        """Time a single operation and return the duration in milliseconds"""
        print(f"  ⏱️  {operation_name}...", end=" ", flush=True)
        start_time = time.time()
        
        try:
            result = operation_func(*args, **kwargs)
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            print(f"{duration_ms:.1f}ms")
            return duration_ms, result
        except Exception as e:
            print(f"FAILED: {e}")
            return None, None
    
    def create_memory_load_script(self, size_mb):
        """Create a Python script that loads the specified amount of data into memory"""
        if size_mb == 0:
            return """
import time
print("Baseline: No additional memory load")
# Just keep the process alive
while True:
    time.sleep(1)
"""
        
        # Calculate array size for the target memory usage
        # numpy float64 uses 8 bytes per element
        elements_needed = (size_mb * 1024 * 1024) // 8
        
        return f"""
import numpy as np
import time
import sys

print("Loading {size_mb}MB of data into memory...")

try:
    # Create numpy array with random data
    data = np.random.random({elements_needed})
    actual_size_mb = data.nbytes / (1024 * 1024)
    print(f"Successfully loaded {{actual_size_mb:.1f}}MB into memory")
    print(f"Array shape: {{data.shape}}")
    print(f"Array dtype: {{data.dtype}}")
    
    # Keep the data in memory and the process alive
    counter = 0
    while True:
        # Occasionally access the data to ensure it stays in memory
        if counter % 10 == 0:
            checksum = np.sum(data[:1000])  # Access small portion
            print(f"Memory check {{counter}}: checksum={{checksum:.2f}}")
        counter += 1
        time.sleep(1)
        
except MemoryError as e:
    print(f"MemoryError: {{e}}")
    sys.exit(1)
except Exception as e:
    print(f"Error: {{e}}")
    sys.exit(1)
"""
    
    def setup_instance_with_memory_load(self, memory_size_mb, test_name):
        """Create an instance and load it with the specified amount of memory"""
        print(f"\n🏗️  Setting up instance for {test_name} test...")
        
        # Create initial snapshot with appropriate memory allocation
        # Use more VM memory for larger loads
        vm_memory = max(256, memory_size_mb + 128)  # Base memory + load + overhead
        
        duration, snapshot = self.time_operation(
            f"Creating base snapshot (VM memory: {vm_memory}MB)",
            self.client.snapshots.create,
            image_id="morphvm-minimal",
            vcpus=1,
            memory=vm_memory,
            disk_size=700
        )
        
        if not snapshot:
            raise Exception("Failed to create base snapshot")
        
        # Start instance
        duration, instance = self.time_operation(
            "Starting base instance",
            self.client.instances.start,
            snapshot.id
        )
        
        if not instance:
            raise Exception("Failed to start base instance")
        
        # Install numpy if not already available
        print("  📦 Installing numpy...")
        instance.exec("pip3 install numpy")
        
        # Create and run the memory loading script
        print(f"  💾 Loading {memory_size_mb}MB into memory...")
        memory_script = self.create_memory_load_script(memory_size_mb)
        
        # Write script to file
        instance.exec(f"cat > /tmp/memory_load.py << 'EOF'\n{memory_script}\nEOF")
        
        # Start the memory loading script in background
        instance.exec("nohup python3 /tmp/memory_load.py > /tmp/memory_load.log 2>&1 &")
        
        # Wait for memory to be loaded
        if memory_size_mb > 0:
            print(f"  ⏳ Waiting for {memory_size_mb}MB to load...")
            time.sleep(10 if memory_size_mb < 100 else 30)  # More time for larger loads
            
            # Check if loading was successful
            try:
                log_output = instance.exec("tail -5 /tmp/memory_load.log").stdout
                print(f"  📋 Memory load status: {log_output.strip()}")
            except:
                print("  ⚠️  Could not check memory load status")
        
        return instance, snapshot
    
    def benchmark_memory_scenario(self, memory_size_mb, test_name, num_tests=5):
        """Benchmark snapshot and startup times for a specific memory scenario"""
        print(f"\n{'='*60}")
        print(f"🧪 TESTING: {test_name.upper()}")
        print(f"{'='*60}")
        
        # Setup instance with memory load
        instance, base_snapshot = self.setup_instance_with_memory_load(memory_size_mb, test_name)
        
        snapshots_to_cleanup = []
        instances_to_cleanup = []
        
        try:
            # Benchmark snapshot creation
            print(f"\n📸 Benchmarking snapshot creation with {memory_size_mb}MB load ({num_tests} tests)...")
            
            for i in range(num_tests):
                print(f"  Test {i+1}/{num_tests}:")
                duration, snapshot = self.time_operation(
                    f"snapshot creation #{i+1}",
                    instance.snapshot
                )
                
                if duration:
                    self.results[test_name]['snapshot_times'].append(duration)
                    if snapshot:
                        snapshots_to_cleanup.append(snapshot)
            
            # Create one final snapshot for startup tests
            print(f"\n📸 Creating snapshot for startup tests...")
            duration, startup_snapshot = self.time_operation(
                "final snapshot for startup tests",
                instance.snapshot
            )
            
            if not startup_snapshot:
                raise Exception("Failed to create startup test snapshot")
            
            # Benchmark instance startup from snapshot
            print(f"\n🚀 Benchmarking startup from snapshot with {memory_size_mb}MB load ({num_tests} tests)...")
            
            for i in range(num_tests):
                print(f"  Test {i+1}/{num_tests}:")
                duration, new_instance = self.time_operation(
                    f"startup from snapshot #{i+1}",
                    self.client.instances.start,
                    startup_snapshot.id
                )
                
                if duration:
                    self.results[test_name]['startup_times'].append(duration)
                    if new_instance:
                        instances_to_cleanup.append(new_instance)
            
            snapshots_to_cleanup.append(startup_snapshot)
            
        finally:
            # Cleanup
            print(f"\n🧹 Cleaning up {test_name} test resources...")
            
            # Stop main instance
            try:
                instance.stop()
            except:
                pass
            
            # Stop test instances
            for inst in instances_to_cleanup:
                try:
                    inst.stop()
                except:
                    pass
            
            # Delete snapshots
            for snap in snapshots_to_cleanup:
                try:
                    snap.delete()
                except:
                    pass
            
            # Delete base snapshot
            try:
                base_snapshot.delete()
            except:
                pass
    
    def run_full_benchmark(self):
        """Run the complete memory size benchmark"""
        print("🧠 Morph Cloud Memory Size Impact Benchmark")
        print("=" * 60)
        print("This benchmark measures how memory usage affects Morph Cloud performance")
        print("by testing snapshot and startup times with different memory loads.\n")
        
        # Test scenarios
        scenarios = [
            (0, 'baseline'),
            (1, '1mb_load'),
            (1024, '1gb_load')
        ]
        
        for memory_mb, test_name in scenarios:
            try:
                self.benchmark_memory_scenario(memory_mb, test_name)
            except Exception as e:
                print(f"❌ Failed {test_name} test: {e}")
                continue
    
    def print_results(self):
        """Print comprehensive benchmark results comparing memory scenarios"""
        print("\n" + "="*70)
        print("🏆 MEMORY SIZE IMPACT BENCHMARK RESULTS")
        print("="*70)
        
        def print_scenario_stats(scenario_name, data):
            memory_mb = data['memory_usage']
            snapshot_times = data['snapshot_times']
            startup_times = data['startup_times']
            
            print(f"\n📊 {scenario_name.upper()} (Memory Load: {memory_mb}MB)")
            print("-" * 50)
            
            if snapshot_times:
                avg_snapshot = statistics.mean(snapshot_times)
                median_snapshot = statistics.median(snapshot_times)
                min_snapshot = min(snapshot_times)
                max_snapshot = max(snapshot_times)
                
                print(f"  📸 Snapshot Creation:")
                print(f"    Average: {avg_snapshot:.1f}ms")
                print(f"    Median:  {median_snapshot:.1f}ms")
                print(f"    Range:   {min_snapshot:.1f}ms - {max_snapshot:.1f}ms")
                print(f"    Samples: {len(snapshot_times)}")
            
            if startup_times:
                avg_startup = statistics.mean(startup_times)
                median_startup = statistics.median(startup_times)
                min_startup = min(startup_times)
                max_startup = max(startup_times)
                
                print(f"  🚀 Instance Startup:")
                print(f"    Average: {avg_startup:.1f}ms")
                print(f"    Median:  {median_startup:.1f}ms")
                print(f"    Range:   {min_startup:.1f}ms - {max_startup:.1f}ms")
                print(f"    Samples: {len(startup_times)}")
        
        # Print results for each scenario
        for scenario_name, data in self.results.items():
            print_scenario_stats(scenario_name, data)
        
        # Comparative analysis
        print("\n" + "="*70)
        print("📈 COMPARATIVE ANALYSIS")
        print("="*70)
        
        baseline_snapshot = self.results['baseline']['snapshot_times']
        baseline_startup = self.results['baseline']['startup_times']
        
        if baseline_snapshot and baseline_startup:
            baseline_snap_avg = statistics.mean(baseline_snapshot)
            baseline_start_avg = statistics.mean(baseline_startup)
            
            print(f"\nBaseline Performance:")
            print(f"  📸 Snapshot: {baseline_snap_avg:.1f}ms")
            print(f"  🚀 Startup:  {baseline_start_avg:.1f}ms")
            
            for scenario in ['1mb_load', '1gb_load']:
                if (self.results[scenario]['snapshot_times'] and 
                    self.results[scenario]['startup_times']):
                    
                    snap_avg = statistics.mean(self.results[scenario]['snapshot_times'])
                    start_avg = statistics.mean(self.results[scenario]['startup_times'])
                    memory_mb = self.results[scenario]['memory_usage']
                    
                    snap_ratio = snap_avg / baseline_snap_avg
                    start_ratio = start_avg / baseline_start_avg
                    
                    print(f"\n{memory_mb}MB Load Impact:")
                    print(f"  📸 Snapshot: {snap_avg:.1f}ms ({snap_ratio:.2f}x baseline)")
                    print(f"  🚀 Startup:  {start_avg:.1f}ms ({start_ratio:.2f}x baseline)")
        
        print(f"\n💡 Key Insights:")
        print(f"• Memory size impact on Morph Cloud's Infinibranch performance")
        print(f"• Demonstrates state preservation efficiency across memory loads")
        print(f"• Shows scalability of snapshot/restore technology")

def main():
    try:
        benchmark = MemorySizeBenchmark()
        benchmark.run_full_benchmark()
        benchmark.print_results()
        
    except Exception as e:
        print(f"❌ Benchmark failed: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure MORPH_API_KEY is set in your .env file")
        print("2. Check your internet connection")
        print("3. Verify you have credits in your Morph Cloud account")
        print("4. Ensure you have sufficient quota for multiple instances")
        print("5. Large memory tests may require higher VM memory limits")

if __name__ == "__main__":
    main()
