#!/usr/bin/env python3
"""
Morph Cloud Branching Benchmark

This script demonstrates the power of Morph's Infinibranch technology by:
- Creating a base VM instance
- Branching it into 100 parallel VMs
- Having each VM report a random coin flip
- Shutting down all VMs

This showcases the ability to instantly create massive parallel workloads
from a single VM state - perfect for AI agent swarms and parallel processing.
"""

import time
import random
import asyncio
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from morphcloud.api import MorphCloudClient
from dotenv import load_dotenv
import os

# Load environment variables from .env file
load_dotenv()

class MorphBranchingBenchmark:
    def __init__(self):
        if not os.getenv('MORPH_API_KEY'):
            raise ValueError("MORPH_API_KEY must be set in .env file or environment")
        
        self.client = MorphCloudClient()
        self.base_instance = None
        self.branched_instances = []
        self.coin_flip_results = []
        self.timing_results = {
            'base_creation_time': 0,
            'snapshot_creation_time': 0,
            'branching_times': [],
            'coin_flip_times': [],
            'shutdown_times': []
        }
    
    def time_operation(self, operation_name, operation_func, *args, **kwargs):
        """Time a single operation and return the duration in milliseconds"""
        print(f"  ⏱️  {operation_name}...", end=" ", flush=True)
        start_time = time.time()
        
        try:
            result = operation_func(*args, **kwargs)
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            print(f"{duration_ms:.1f}ms")
            return duration_ms, result
        except Exception as e:
            print(f"FAILED: {e}")
            return None, None
    
    def create_base_instance(self):
        """Create the base VM instance that will be branched"""
        print("\n🏗️  Creating base VM instance...")
        
        # Create initial snapshot
        duration, snapshot = self.time_operation(
            "Creating base snapshot",
            self.client.snapshots.create,
            image_id="morphvm-minimal",
            vcpus=1,
            memory=128,
            disk_size=700
        )
        
        if not snapshot:
            raise Exception("Failed to create base snapshot")
        
        # Start base instance
        duration, instance = self.time_operation(
            "Starting base instance",
            self.client.instances.start,
            snapshot.id
        )
        
        if not instance:
            raise Exception("Failed to start base instance")
        
        self.timing_results['base_creation_time'] = duration
        self.base_instance = instance
        
        # Set up coin flip script in the base instance
        print("  📝 Setting up coin flip script...")
        
        coin_flip_script = """#!/bin/bash
# Simple coin flip script that outputs result to a file
RESULT=$((RANDOM % 2))
if [ $RESULT -eq 0 ]; then
    echo "HEADS" > /tmp/coin_flip_result.txt
else
    echo "TAILS" > /tmp/coin_flip_result.txt
fi
echo "Coin flip result: $(cat /tmp/coin_flip_result.txt)"
"""
        
        instance.exec(f"cat > /tmp/coin_flip.sh << 'EOF'\n{coin_flip_script}\nEOF")
        instance.exec("chmod +x /tmp/coin_flip.sh")
        
        print("  ✅ Base instance setup complete")
        return instance
    
    def create_snapshot_for_branching(self):
        """Create a snapshot of the base instance for branching"""
        print("\n📸 Creating snapshot for branching...")
        
        duration, snapshot = self.time_operation(
            "Creating branching snapshot",
            self.base_instance.snapshot
        )
        
        if not snapshot:
            raise Exception("Failed to create branching snapshot")
        
        self.timing_results['snapshot_creation_time'] = duration
        return snapshot
    
    def branch_instance(self, snapshot_id, instance_number):
        """Branch a single instance from the snapshot"""
        try:
            start_time = time.time()
            new_instance = self.client.instances.start(snapshot_id)
            end_time = time.time()
            
            duration_ms = (end_time - start_time) * 1000
            return duration_ms, new_instance, instance_number
        except Exception as e:
            print(f"❌ Failed to branch instance {instance_number}: {e}")
            return None, None, instance_number
    
    def get_coin_flip_result(self, instance, instance_number):
        """Execute coin flip and get result from an instance"""
        try:
            start_time = time.time()
            
            # Execute the coin flip script
            instance.exec("/tmp/coin_flip.sh")
            
            # Get the result
            result = instance.exec("cat /tmp/coin_flip_result.txt").strip()
            
            end_time = time.time()
            duration_ms = (end_time - start_time) * 1000
            
            return duration_ms, result, instance_number
        except Exception as e:
            print(f"❌ Failed to get coin flip from instance {instance_number}: {e}")
            return None, None, instance_number
    
    def shutdown_instance(self, instance, instance_number):
        """Shutdown a single instance"""
        try:
            start_time = time.time()
            instance.stop()
            end_time = time.time()
            
            duration_ms = (end_time - start_time) * 1000
            return duration_ms, instance_number
        except Exception as e:
            print(f"❌ Failed to shutdown instance {instance_number}: {e}")
            return None, instance_number
    
    def run_branching_benchmark(self, num_branches=100):
        """Run the complete branching benchmark"""
        print(f"\n🌳 Branching into {num_branches} parallel VMs...")
        
        # Create snapshot for branching
        snapshot = self.create_snapshot_for_branching()
        
        # Branch instances in parallel
        print(f"\n🚀 Creating {num_branches} branched instances...")
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            # Submit all branching tasks
            branching_futures = [
                executor.submit(self.branch_instance, snapshot.id, i+1)
                for i in range(num_branches)
            ]
            
            # Collect results
            successful_instances = []
            for future in as_completed(branching_futures):
                duration, instance, instance_num = future.result()
                if duration and instance:
                    self.timing_results['branching_times'].append(duration)
                    successful_instances.append((instance, instance_num))
                    print(f"  ✅ Instance {instance_num} created in {duration:.1f}ms")
                else:
                    print(f"  ❌ Instance {instance_num} failed to create")
        
        self.branched_instances = successful_instances
        print(f"\n🎉 Successfully created {len(successful_instances)} instances!")
        
        # Execute coin flips in parallel
        print(f"\n🪙 Getting coin flip results from {len(successful_instances)} instances...")
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            # Submit all coin flip tasks
            coin_flip_futures = [
                executor.submit(self.get_coin_flip_result, instance, instance_num)
                for instance, instance_num in successful_instances
            ]
            
            # Collect results
            for future in as_completed(coin_flip_futures):
                duration, result, instance_num = future.result()
                if duration and result:
                    self.timing_results['coin_flip_times'].append(duration)
                    self.coin_flip_results.append((instance_num, result))
                    print(f"  🪙 Instance {instance_num}: {result} ({duration:.1f}ms)")
        
        # Shutdown all instances in parallel
        print(f"\n🛑 Shutting down all {len(successful_instances)} instances...")
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            # Submit all shutdown tasks
            shutdown_futures = [
                executor.submit(self.shutdown_instance, instance, instance_num)
                for instance, instance_num in successful_instances
            ]
            
            # Collect results
            for future in as_completed(shutdown_futures):
                duration, instance_num = future.result()
                if duration:
                    self.timing_results['shutdown_times'].append(duration)
                    print(f"  ✅ Instance {instance_num} shutdown in {duration:.1f}ms")
        
        # Cleanup base instance and snapshot
        print("\n🧹 Cleaning up base resources...")
        try:
            self.base_instance.stop()
            snapshot.delete()
            print("  ✅ Base cleanup complete")
        except Exception as e:
            print(f"  ⚠️  Cleanup warning: {e}")
    
    def print_results(self):
        """Print comprehensive benchmark results"""
        print("\n" + "="*70)
        print("🏆 MORPH CLOUD BRANCHING BENCHMARK RESULTS")
        print("="*70)
        
        # Coin flip summary
        heads_count = sum(1 for _, result in self.coin_flip_results if result == "HEADS")
        tails_count = sum(1 for _, result in self.coin_flip_results if result == "TAILS")
        total_flips = len(self.coin_flip_results)
        
        print(f"\n🪙 COIN FLIP RESULTS:")
        print(f"  📊 Total flips: {total_flips}")
        print(f"  👑 HEADS: {heads_count} ({heads_count/total_flips*100:.1f}%)")
        print(f"  🔄 TAILS: {tails_count} ({tails_count/total_flips*100:.1f}%)")
        
        # Performance statistics
        def print_stats(operation_name, times):
            if not times:
                print(f"\n{operation_name}: No data collected")
                return
            
            avg = statistics.mean(times)
            median = statistics.median(times)
            min_time = min(times)
            max_time = max(times)
            total_time = sum(times)
            
            print(f"\n{operation_name}:")
            print(f"  📊 Average: {avg:.1f}ms")
            print(f"  📈 Median:  {median:.1f}ms")
            print(f"  ⚡ Fastest: {min_time:.1f}ms")
            print(f"  🐌 Slowest: {max_time:.1f}ms")
            print(f"  ⏱️  Total:   {total_time:.1f}ms ({total_time/1000:.1f}s)")
            print(f"  📋 Samples: {len(times)}")
        
        print_stats("🚀 Instance Branching", self.timing_results['branching_times'])
        print_stats("🪙 Coin Flip Execution", self.timing_results['coin_flip_times'])
        print_stats("🛑 Instance Shutdown", self.timing_results['shutdown_times'])
        
        # Summary insights
        print("\n" + "="*70)
        print("💡 PERFORMANCE INSIGHTS")
        print("="*70)
        
        if self.timing_results['branching_times']:
            avg_branch = statistics.mean(self.timing_results['branching_times'])
            total_branch_time = sum(self.timing_results['branching_times'])
            print(f"• Average branching time: {avg_branch:.1f}ms per instance")
            print(f"• Total time to create {len(self.timing_results['branching_times'])} instances: {total_branch_time/1000:.1f}s")
            print(f"• That's {len(self.timing_results['branching_times'])/(total_branch_time/1000):.1f} instances per second!")
        
        if self.timing_results['coin_flip_times']:
            avg_flip = statistics.mean(self.timing_results['coin_flip_times'])
            print(f"• Average coin flip execution: {avg_flip:.1f}ms per instance")
        
        if self.timing_results['shutdown_times']:
            avg_shutdown = statistics.mean(self.timing_results['shutdown_times'])
            print(f"• Average shutdown time: {avg_shutdown:.1f}ms per instance")
        
        print(f"\n🌟 Successfully demonstrated parallel execution across {total_flips} VMs!")
        print("   This showcases the power of Infinibranch for AI agent swarms!")

def main():
    print("🌳 Morph Cloud Branching Benchmark")
    print("==================================")
    print("This benchmark demonstrates the power of Infinibranch technology")
    print("by creating 100 parallel VMs from a single base instance,")
    print("executing coin flips, and shutting everything down.\n")
    
    try:
        benchmark = MorphBranchingBenchmark()
        
        # Create base instance
        benchmark.create_base_instance()
        
        # Run the branching benchmark
        benchmark.run_branching_benchmark(num_branches=100)
        
        # Print results
        benchmark.print_results()
    
    except Exception as e:
        print(f"❌ Benchmark failed: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure MORPH_API_KEY is set in your .env file")
        print("2. Check your internet connection")
        print("3. Verify you have credits in your Morph Cloud account")
        print("4. Ensure you have sufficient quota for 100+ instances")

if __name__ == "__main__":
    main()
