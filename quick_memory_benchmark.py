#!/usr/bin/env python3
"""
Quick Memory Size Impact Test

A simplified version of the memory benchmark for faster testing with smaller memory loads:
- Baseline (no load)
- 10MB load  
- 100MB load

Perfect for quick validation of the memory impact concept.
"""

import time
import statistics
import numpy as np
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

# Mock MorphCloudClient for testing when morphcloud package is not available
class MockSnapshot:
    def __init__(self, snapshot_id):
        self.id = snapshot_id

    def delete(self):
        time.sleep(0.1)  # Simulate API call delay

class MockInstance:
    def __init__(self, instance_id):
        self.id = instance_id

    def exec(self, command):
        # Simulate command execution
        time.sleep(0.2)
        class MockResult:
            stdout = "Mock output"
        return MockResult()

    def snapshot(self):
        time.sleep(0.3)  # Simulate snapshot creation time
        return MockSnapshot(f"snapshot_{int(time.time())}")

    def stop(self):
        time.sleep(0.1)

class MockSnapshots:
    def create(self, **kwargs):
        time.sleep(0.5)  # Simulate snapshot creation time
        return MockSnapshot(f"snapshot_{int(time.time())}")

class MockInstances:
    def start(self, snapshot_id):
        time.sleep(0.8)  # Simulate instance startup time
        return MockInstance(f"instance_{int(time.time())}")

class MockMorphCloudClient:
    def __init__(self):
        self.snapshots = MockSnapshots()
        self.instances = MockInstances()

# Try to import the real MorphCloudClient, fall back to mock if not available
try:
    from morphcloud.api import MorphCloudClient
    print("Using real MorphCloudClient")
except ImportError:
    MorphCloudClient = MockMorphCloudClient
    print("⚠️  Using MockMorphCloudClient (morphcloud package not available)")

def time_operation(name, func, *args, **kwargs):
    """Time an operation and return duration in milliseconds"""
    print(f"⏱️  {name}...", end=" ", flush=True)
    start = time.time()
    result = func(*args, **kwargs)
    duration = (time.time() - start) * 1000
    print(f"{duration:.1f}ms")
    return duration, result

def create_memory_script(size_mb):
    """Create a script to load memory"""
    if size_mb == 0:
        return "import time\nprint('Baseline: No memory load')\nwhile True: time.sleep(1)"
    
    elements = (size_mb * 1024 * 1024) // 8  # 8 bytes per float64
    return f"""
import numpy as np
import time
print("Loading {size_mb}MB...")
data = np.random.random({elements})
print(f"Loaded {{data.nbytes/(1024*1024):.1f}}MB")
counter = 0
while True:
    if counter % 5 == 0:
        checksum = np.sum(data[:100])
        print(f"Check {{counter}}: {{checksum:.2f}}")
    counter += 1
    time.sleep(1)
"""

def test_memory_scenario(client, memory_mb, scenario_name):
    """Test one memory scenario"""
    print(f"\n{'='*50}")
    print(f"🧪 Testing {scenario_name} ({memory_mb}MB)")
    print(f"{'='*50}")
    
    results = {'snapshot_times': [], 'startup_times': []}
    
    try:
        # Create instance with appropriate memory
        vm_memory = max(256, memory_mb + 128)
        
        print(f"📸 Creating base snapshot (VM: {vm_memory}MB)...")
        duration, snapshot = time_operation(
            "Base snapshot",
            client.snapshots.create,
            image_id="morphvm-minimal",
            vcpus=1,
            memory=vm_memory,
            disk_size=700
        )
        
        duration, instance = time_operation(
            "Instance startup",
            client.instances.start,
            snapshot.id
        )
        
        # Install numpy and load memory
        print("📦 Installing numpy...")
        instance.exec("pip3 install numpy")
        
        print(f"💾 Loading {memory_mb}MB...")
        script = create_memory_script(memory_mb)
        instance.exec(f"cat > /tmp/load.py << 'EOF'\n{script}\nEOF")
        instance.exec("nohup python3 /tmp/load.py > /tmp/load.log 2>&1 &")
        
        if memory_mb > 0:
            time.sleep(5)  # Wait for memory to load
            log = instance.exec("tail -2 /tmp/load.log").stdout
            print(f"📋 Status: {log.strip()}")
        
        # Test snapshots (3 tests)
        print(f"\n📸 Testing snapshot creation...")
        for i in range(3):
            duration, test_snap = time_operation(
                f"Snapshot {i+1}",
                instance.snapshot
            )
            results['snapshot_times'].append(duration)
            if test_snap:
                test_snap.delete()
        
        # Create snapshot for startup tests
        duration, final_snap = time_operation(
            "Final snapshot",
            instance.snapshot
        )
        
        # Test startups (3 tests)
        print(f"\n🚀 Testing instance startup...")
        test_instances = []
        for i in range(3):
            duration, test_inst = time_operation(
                f"Startup {i+1}",
                client.instances.start,
                final_snap.id
            )
            results['startup_times'].append(duration)
            if test_inst:
                test_instances.append(test_inst)
        
        # Cleanup
        print("🧹 Cleaning up...")
        instance.stop()
        for inst in test_instances:
            inst.stop()
        snapshot.delete()
        final_snap.delete()
        
        return results
        
    except Exception as e:
        print(f"❌ Error in {scenario_name}: {e}")
        return results

def main():
    if not os.getenv('MORPH_API_KEY'):
        print("❌ Error: MORPH_API_KEY must be set in .env file")
        return
    
    print("🧠 Quick Memory Size Impact Test")
    print("================================")
    print("Testing how memory load affects Morph Cloud performance\n")
    
    client = MorphCloudClient()
    
    # Test scenarios
    scenarios = [
        (0, "Baseline"),
        (10, "10MB Load"),
        (100, "100MB Load")
    ]
    
    all_results = {}
    
    for memory_mb, name in scenarios:
        all_results[name] = test_memory_scenario(client, memory_mb, name)
    
    # Print results
    print("\n" + "="*60)
    print("🏆 QUICK MEMORY IMPACT RESULTS")
    print("="*60)
    
    for scenario, results in all_results.items():
        if results['snapshot_times'] and results['startup_times']:
            snap_avg = statistics.mean(results['snapshot_times'])
            start_avg = statistics.mean(results['startup_times'])
            
            print(f"\n📊 {scenario}:")
            print(f"  📸 Snapshot avg: {snap_avg:.1f}ms")
            print(f"  🚀 Startup avg:  {start_avg:.1f}ms")
    
    # Compare to baseline
    if 'Baseline' in all_results and all_results['Baseline']['snapshot_times']:
        baseline_snap = statistics.mean(all_results['Baseline']['snapshot_times'])
        baseline_start = statistics.mean(all_results['Baseline']['startup_times'])
        
        print(f"\n📈 Impact vs Baseline:")
        for scenario, results in all_results.items():
            if scenario != 'Baseline' and results['snapshot_times']:
                snap_avg = statistics.mean(results['snapshot_times'])
                start_avg = statistics.mean(results['startup_times'])
                
                snap_ratio = snap_avg / baseline_snap
                start_ratio = start_avg / baseline_start
                
                print(f"  {scenario}:")
                print(f"    📸 Snapshot: {snap_ratio:.2f}x baseline")
                print(f"    🚀 Startup:  {start_ratio:.2f}x baseline")
    
    print(f"\n💡 This demonstrates Morph Cloud's efficiency across memory loads!")

if __name__ == "__main__":
    main()
