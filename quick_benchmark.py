#!/usr/bin/env python3
"""
Quick Morph Cloud Performance Test

A simplified benchmark that quickly demonstrates the speed of:
- Snapshot creation
- Instance startup from snapshots

Perfect for a quick demo of Infinibranch performance.
"""

import time
from morphcloud.api import MorphCloudClient
from dotenv import load_dotenv
import os

# Load environment variables
load_dotenv()

def time_operation(name, func, *args, **kwargs):
    """Time an operation and return duration in milliseconds"""
    print(f"⏱️  {name}...", end=" ", flush=True)
    start = time.time()
    result = func(*args, **kwargs)
    duration = (time.time() - start) * 1000
    print(f"{duration:.1f}ms")
    return duration, result

def main():
    if not os.getenv('MORPH_API_KEY'):
        print("❌ Error: MORPH_API_KEY must be set in .env file")
        return
    
    print("🚀 Quick Morph Cloud Performance Test")
    print("=====================================\n")
    
    client = MorphCloudClient()
    
    try:
        # Step 1: Create initial snapshot
        print("📸 Creating initial snapshot...")
        duration, snapshot = time_operation(
            "Initial snapshot creation",
            client.snapshots.create,
            image_id="morphvm-minimal",
            vcpus=1,
            memory=128,
            disk_size=700
        )
        
        # Step 2: Start instance
        print("\n🚀 Starting instance from snapshot...")
        startup_duration, instance = time_operation(
            "Instance startup",
            client.instances.start,
            snapshot.id
        )
        
        # Step 3: Add some workload
        print("\n⚙️  Adding workload to instance...")
        instance.exec("echo 'Creating workload...' && for i in {1..50}; do echo 'File $i' > /tmp/file_$i.txt; done")
        instance.exec("nohup bash -c 'count=1; while [ $count -le 100 ]; do echo $count > /tmp/counter.txt; sleep 0.5; count=$((count + 1)); done' &")
        
        # Wait for workload to start
        time.sleep(2)
        
        # Step 4: Snapshot the running instance
        print("\n📸 Creating snapshot of running instance...")
        snapshot_duration, running_snapshot = time_operation(
            "Running instance snapshot",
            instance.snapshot
        )
        
        # Step 5: Start new instance from running snapshot
        print("\n🔄 Starting new instance from running snapshot...")
        new_startup_duration, new_instance = time_operation(
            "New instance from running snapshot",
            client.instances.start,
            running_snapshot.id
        )
        
        # Step 6: Verify state preservation
        print("\n🎯 Verifying state preservation...")
        original_counter = instance.exec("cat /tmp/counter.txt").stdout.strip()
        new_counter = new_instance.exec("cat /tmp/counter.txt").stdout.strip()
        
        print(f"   Original counter: {original_counter}")
        print(f"   New instance counter: {new_counter}")
        
        # Results summary
        print("\n" + "="*50)
        print("🏆 PERFORMANCE RESULTS")
        print("="*50)
        print(f"📸 Snapshot creation:     {snapshot_duration:.1f}ms")
        print(f"🚀 Instance startup:      {startup_duration:.1f}ms")
        print(f"📸 Running snapshot:      {snapshot_duration:.1f}ms")
        print(f"🔄 Startup from snapshot: {new_startup_duration:.1f}ms")
        
        print(f"\n💡 Traditional VMs take 120-180 seconds to start")
        speedup = 150000 / startup_duration  # 2.5 min = 150,000ms
        print(f"   Morph Cloud is ~{speedup:.0f}x faster! 🚀")
        
        print(f"\n✨ State perfectly preserved: {original_counter} → {new_counter}")
        
        # Cleanup
        print("\n🧹 Cleaning up...")
        instance.stop()
        new_instance.stop()
        snapshot.delete()
        running_snapshot.delete()
        print("✅ Cleanup complete")
        
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    main()
