#!/usr/bin/env python3
"""
Morph Cloud Example: Perfect State Preservation Demo

This example demonstrates the core magic of Morph Cloud's Infinibranch technology:
- Creating snapshots of running computational environments
- Perfect preservation of running processes and state
- Sub-second instance creation from snapshots

Before running this example:
1. Sign up for a free account at https://cloud.morph.so
2. Get your API key from the dashboard
3. Install the morphcloud package: pip install morphcloud
4. Set your API key: export MORPH_API_KEY='your-api-key-here'
"""

from morphcloud.api import MorphCloudClient
import time
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

def main():
    # Check for API key
    if not os.getenv('MORPH_API_KEY'):
        print("❌ Error: MORPH_API_KEY environment variable must be set")
        print("   Get your API key from https://cloud.morph.so")
        print("   Then either:")
        print("   • Add MORPH_API_KEY='your-api-key-here' to your .env file, or")
        print("   • Run: export MORPH_API_KEY='your-api-key-here'")
        return
    
    print("🚀 Starting Morph Cloud State Preservation Demo")
    print("=" * 50)
    
    # Initialize client
    client = MorphCloudClient()
    
    try:
        # Step 1: Create initial snapshot and start instance
        print("\n📸 Step 1: Creating snapshot and starting instance...")
        snapshot = client.snapshots.create(
            image_id="morphvm-minimal", 
            vcpus=1, 
            memory=128, 
            disk_size=700
        )
        print(f"   Created snapshot: {snapshot.id}")
        
        instance = client.instances.start(snapshot.id)
        print(f"   Started instance: {instance.id}")
        
        # Step 2: Create a background counter process
        print("\n⚙️  Step 2: Creating background counter process...")
        counter_script = """#!/bin/bash
count=1
echo $count > /root/counter.txt
while [ $count -le 100 ]; do
  sleep 1
  count=$((count + 1))
  echo $count > /root/counter.txt
  echo "Counter is now: $count" >> /root/counter.log
done
"""
        
        # Upload and start the counter script
        instance.exec(f"cat > /root/counter_script.sh << 'EOF'\n{counter_script}\nEOF")
        instance.exec("chmod +x /root/counter_script.sh")
        instance.exec("nohup /root/counter_script.sh > /dev/null 2>&1 &")
        print("   Background counter process started!")
        
        # Step 3: Monitor the counter
        print("\n📊 Step 3: Monitoring counter progress...")
        time.sleep(2)
        initial_counter = instance.exec("cat /root/counter.txt").stdout.strip()
        print(f"   Initial counter value: {initial_counter}")
        
        print("   Waiting 10 seconds for counter to increment...")
        time.sleep(10)
        incremented_counter = instance.exec("cat /root/counter.txt").stdout.strip()
        print(f"   Counter after waiting: {incremented_counter}")
        
        # Step 4: The Magic - Snapshot the running instance
        print("\n✨ Step 4: Creating snapshot of RUNNING instance...")
        print("   This captures the entire state including the running counter process!")
        new_snapshot = instance.snapshot()
        print(f"   Created snapshot with running process: {new_snapshot.id}")
        
        # Step 5: Start new instance from the snapshot
        print("\n🔄 Step 5: Starting new instance from snapshot...")
        new_instance = client.instances.start(new_snapshot.id)
        print(f"   Started new instance: {new_instance.id}")
        
        # Step 6: Verify perfect state preservation
        print("\n🎯 Step 6: Verifying perfect state preservation...")
        new_counter = new_instance.exec("cat /root/counter.txt").stdout.strip()
        print(f"   Counter in new instance: {new_counter}")
        
        print("   Waiting 10 seconds to verify counter continues...")
        time.sleep(10)
        final_counter = new_instance.exec("cat /root/counter.txt").stdout.strip()
        print(f"   Counter after waiting in new instance: {final_counter}")
        
        # Show the magic
        print("\n" + "=" * 50)
        print("🎉 SUCCESS! Perfect State Preservation Demonstrated!")
        print("=" * 50)
        print(f"Original instance counter: {incremented_counter} → New instance counter: {final_counter}")
        print("\n🔮 What just happened:")
        print("   • The background process continued running exactly where it left off")
        print("   • No process restart or state loss occurred")
        print("   • The new instance started in milliseconds, not minutes")
        print("   • Complete environment state was preserved perfectly")
        
        # Show log to prove continuity
        print("\n📝 Counter log from new instance (showing continuity):")
        log_output = new_instance.exec("tail -5 /root/counter.log").stdout
        print(log_output)
        
        print("\n🌟 This is the power of Morph Cloud's Infinibranch technology!")
        print("   Perfect for AI agents that need to explore parallel paths")
        print("   or recover from failures without losing computational state.")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        print("\nTroubleshooting:")
        print("1. Make sure you have a valid MORPH_API_KEY")
        print("2. Check your internet connection")
        print("3. Verify you have credits in your Morph Cloud account")

if __name__ == "__main__":
    main()
