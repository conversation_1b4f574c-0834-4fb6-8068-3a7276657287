#!/bin/bash

# Morph Cloud CLI Example: Perfect State Preservation Demo
# 
# This script demonstrates Morph Cloud's Infinibranch technology using the CLI.
# It shows how to snapshot running processes and restore them perfectly.
#
# Prerequisites:
# 1. Sign up at https://cloud.morph.so and get your API key
# 2. Install morphcloud: pip install morphcloud
# 3. Set API key: export MORPH_API_KEY='your-api-key-here'

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}$1${NC}"
}

print_warning() {
    echo -e "${YELLOW}$1${NC}"
}

print_error() {
    echo -e "${RED}$1${NC}"
}

# Load .env file if it exists
load_env() {
    if [ -f ".env" ]; then
        print_step "📄 Loading environment variables from .env file..."
        export $(grep -v '^#' .env | xargs)
    fi
}

# Check prerequisites
check_prerequisites() {
    print_step "🔍 Checking prerequisites..."

    # Try to load .env file first
    load_env

    if [ -z "$MORPH_API_KEY" ]; then
        print_error "❌ Error: MORPH_API_KEY environment variable must be set"
        echo "   Get your API key from https://cloud.morph.so"
        echo "   Then either:"
        echo "   • Add MORPH_API_KEY='your-api-key-here' to your .env file, or"
        echo "   • Run: export MORPH_API_KEY='your-api-key-here'"
        exit 1
    fi
    
    if ! python3 -c "import morphcloud" 2>/dev/null; then
        print_error "❌ Error: morphcloud package not installed"
        echo "   Install with: pip install morphcloud"
        exit 1
    fi
    
    print_success "✅ Prerequisites check passed!"
}

# Define the CLI command
MORPH_CMD="python3 -m morphcloud.cli"

main() {
    echo "🚀 Morph Cloud CLI State Preservation Demo"
    echo "=========================================="
    
    check_prerequisites
    
    # Step 1: Create snapshot and start instance
    print_step "\n📸 Step 1: Creating snapshot and starting instance..."
    SNAPSHOT_OUTPUT=$($MORPH_CMD snapshot create --image-id=morphvm-minimal --vcpus=1 --memory=128 --disk-size=700 2>&1)
    SNAPSHOT=$(echo "$SNAPSHOT_OUTPUT" | grep -o 'snapshot_[a-zA-Z0-9]*' | tail -1)
    
    if [ -z "$SNAPSHOT" ]; then
        print_error "❌ Failed to create snapshot"
        echo "$SNAPSHOT_OUTPUT"
        exit 1
    fi
    
    print_success "   Created snapshot: $SNAPSHOT"
    
    INSTANCE_OUTPUT=$($MORPH_CMD instance start $SNAPSHOT 2>&1)
    INSTANCE=$(echo "$INSTANCE_OUTPUT" | grep -o 'morphvm_[a-zA-Z0-9]*' | tail -1)
    
    if [ -z "$INSTANCE" ]; then
        print_error "❌ Failed to start instance"
        echo "$INSTANCE_OUTPUT"
        exit 1
    fi
    
    print_success "   Started instance: $INSTANCE"
    
    # Step 2: Create and start counter script
    print_step "\n⚙️  Step 2: Creating background counter process..."
    
    $MORPH_CMD instance exec $INSTANCE "bash -c 'cat > /root/counter_script.sh << \"EOF\"
#!/bin/bash
count=1
echo \$count > /root/counter.txt
while [ \$count -le 100 ]; do
  sleep 5
  count=\$((count + 1))
  echo \$count > /root/counter.txt
  echo \"Counter is now: \$count\" >> /root/counter.log
done
EOF'" > /dev/null 2>&1
    
    $MORPH_CMD instance exec $INSTANCE "chmod +x /root/counter_script.sh" > /dev/null 2>&1
    $MORPH_CMD instance exec $INSTANCE "nohup /root/counter_script.sh > /dev/null 2>&1 &" > /dev/null 2>&1
    
    print_success "   Background counter process started!"
    
    # Step 3: Monitor the counter
    print_step "\n📊 Step 3: Monitoring counter progress..."
    sleep 2
    INITIAL_COUNTER=$($MORPH_CMD instance exec $INSTANCE "cat /root/counter.txt" 2>/dev/null | grep -v "Executing" | grep -v "Command execution" | tail -1)
    print_success "   Initial counter value: $INITIAL_COUNTER"
    
    print_warning "   Waiting 10 seconds for counter to increment..."
    sleep 10
    INCREMENTED_COUNTER=$($MORPH_CMD instance exec $INSTANCE "cat /root/counter.txt" 2>/dev/null | grep -v "Executing" | grep -v "Command execution" | tail -1)
    print_success "   Counter after waiting: $INCREMENTED_COUNTER"
    
    # Step 4: Create snapshot of running instance
    print_step "\n✨ Step 4: Creating snapshot of RUNNING instance..."
    print_warning "   This captures the entire state including the running counter process!"
    
    NEW_SNAPSHOT_OUTPUT=$($MORPH_CMD instance snapshot $INSTANCE 2>&1)
    NEW_SNAPSHOT=$(echo "$NEW_SNAPSHOT_OUTPUT" | grep -o 'snapshot_[a-zA-Z0-9]*' | tail -1)
    
    if [ -z "$NEW_SNAPSHOT" ]; then
        print_error "❌ Failed to create snapshot"
        echo "$NEW_SNAPSHOT_OUTPUT"
        exit 1
    fi
    
    print_success "   Created snapshot with running process: $NEW_SNAPSHOT"
    
    # Step 5: Start new instance from snapshot
    print_step "\n🔄 Step 5: Starting new instance from snapshot..."
    NEW_INSTANCE_OUTPUT=$($MORPH_CMD instance start $NEW_SNAPSHOT 2>&1)
    NEW_INSTANCE=$(echo "$NEW_INSTANCE_OUTPUT" | grep -o 'morphvm_[a-zA-Z0-9]*' | tail -1)
    
    if [ -z "$NEW_INSTANCE" ]; then
        print_error "❌ Failed to start new instance"
        echo "$NEW_INSTANCE_OUTPUT"
        exit 1
    fi
    
    print_success "   Started new instance: $NEW_INSTANCE"
    
    # Step 6: Verify perfect state preservation
    print_step "\n🎯 Step 6: Verifying perfect state preservation..."
    NEW_COUNTER=$($MORPH_CMD instance exec $NEW_INSTANCE "cat /root/counter.txt" 2>/dev/null | grep -v "Executing" | grep -v "Command execution" | tail -1)
    print_success "   Counter in new instance: $NEW_COUNTER"
    
    print_warning "   Waiting 10 seconds to verify counter continues..."
    sleep 10
    FINAL_COUNTER=$($MORPH_CMD instance exec $NEW_INSTANCE "cat /root/counter.txt" 2>/dev/null | grep -v "Executing" | grep -v "Command execution" | tail -1)
    print_success "   Counter after waiting in new instance: $FINAL_COUNTER"
    
    # Show the magic
    echo ""
    echo "=========================================="
    print_success "🎉 SUCCESS! Perfect State Preservation!"
    echo "=========================================="
    echo "Original instance counter: $INCREMENTED_COUNTER → New instance counter: $FINAL_COUNTER"
    echo ""
    print_step "🔮 What just happened:"
    echo "   • The background process continued running exactly where it left off"
    echo "   • No process restart or state loss occurred"
    echo "   • The new instance started in milliseconds, not minutes"
    echo "   • Complete environment state was preserved perfectly"
    
    # Show log to prove continuity
    print_step "\n📝 Counter log from new instance (showing continuity):"
    $MORPH_CMD instance exec $NEW_INSTANCE "tail -5 /root/counter.log" 2>/dev/null | grep -v "Executing" | grep -v "Command execution"
    
    echo ""
    print_success "🌟 This is the power of Morph Cloud's Infinibranch technology!"
    echo "   Perfect for AI agents that need to explore parallel paths"
    echo "   or recover from failures without losing computational state."
    
    echo ""
    print_step "🏃 Instances still running:"
    echo "   Original: $INSTANCE"
    echo "   New:      $NEW_INSTANCE"
    echo ""
    print_warning "💡 Remember to stop instances when done to avoid charges!"
}

# Run the main function
main
